# Turn-Based WebSocket Endpoints Summary

## WebSocket Connection
- **Endpoint**: `/ws`
- **Protocol**: STOMP over WebSocket
- **Message Prefix**: `/app` (for sending to server)
- **Topic Prefix**: `/topic` (for receiving from server)

## Turn Management Endpoints

### 1. Initialize Turns
**Send to**: `/app/initializeTurns/{lobbyId}`
**Listen on**: `/topic/turnsInitialized/{lobbyId}`
- **Purpose**: Initialize turn system when game starts
- **Request**: No body required
- **Response**: `TurnStateResponse`

### 2. Roll Dice
**Send to**: `/app/rollDice/{lobbyId}`
**Listen on**: `/topic/diceRolled/{lobbyId}`
- **Purpose**: Player rolls dice to start their turn
- **Request**: `TurnActionRequest`
- **Response**: `TurnStateResponse`

### 3. Complete Movement
**Send to**: `/app/completeMovement/{lobbyId}`
**Listen on**: `/topic/movementCompleted/{lobbyId}`
- **Purpose**: Signal that player has finished moving
- **Request**: `TurnActionRequest`
- **Response**: `TurnStateResponse`

### 4. Make Suggestion
**Send to**: `/app/makeSuggestion/{lobbyId}`
**Listen on**: `/topic/suggestionMade/{lobbyId}`
- **Purpose**: Make a suggestion when in a room
- **Request**: `SuggestionRequest`
- **Response**: `Map<String, Object>` with success/failure info

### 5. Make Accusation
**Send to**: `/app/makeAccusation/{lobbyId}`
**Listen on**: `/topic/accusationMade/{lobbyId}`
- **Purpose**: Make an accusation to try to win
- **Request**: `AccusationRequest`
- **Response**: `Map<String, Object>` with success/failure info

### 6. Get Turn State
**Send to**: `/app/getTurnState/{lobbyId}`
**Listen on**: `/topic/turnState/{lobbyId}`
- **Purpose**: Get current turn information
- **Request**: No body required
- **Response**: `TurnStateResponse`

### 7. Check Player Turn
**Send to**: `/app/checkPlayerTurn/{lobbyId}`
**Listen on**: `/topic/playerTurnCheck/{lobbyId}`
- **Purpose**: Check if it's a specific player's turn
- **Request**: `{"playerName": "string"}`
- **Response**: `Map<String, Object>` with turn check info

## Data Transfer Objects (DTOs)

### TurnActionRequest
```javascript
{
  playerName: string,
  actionType: string,    // "DICE_ROLL", "MOVEMENT", "SUGGESTION", "ACCUSATION"
  diceValue: number,
  suspect: string,
  weapon: string,
  room: string
}
```

### TurnStateResponse
```javascript
{
  lobbyId: string,
  currentPlayerName: string,
  currentPlayerIndex: number,
  turnState: string,         // See TurnState enum below
  canMakeAccusation: boolean,
  canMakeSuggestion: boolean,
  diceValue: number,
  message: string
}
```

### SuggestionRequest
```javascript
{
  playerName: string,
  suspect: string,
  weapon: string,
  room: string
}
```

### AccusationRequest
```javascript
{
  playerName: string,
  suspect: string,
  weapon: string,
  room: string
}
```

## Turn States
```javascript
const TurnState = {
  WAITING_FOR_PLAYERS: "WAITING_FOR_PLAYERS",     // Lobby created, waiting for players
  WAITING_FOR_START: "WAITING_FOR_START",         // At least 3 players, waiting for host
  PLAYERS_TURN_ROLL_DICE: "PLAYERS_TURN_ROLL_DICE", // Current player needs to roll dice
  PLAYERS_TURN_MOVE: "PLAYERS_TURN_MOVE",         // Current player needs to move
  PLAYERS_TURN_SUSPECT: "PLAYERS_TURN_SUSPECT",   // Current player can make suggestion
  PLAYERS_TURN_SOLVE: "PLAYERS_TURN_SOLVE",       // Current player can make accusation
  PLAYERS_TURN_END: "PLAYERS_TURN_END",           // Current player's turn is ending
  PLAYER_HAS_WON: "PLAYER_HAS_WON"               // Game finished, someone won
}
```

## Frontend Implementation Example

```javascript
// Connect to WebSocket
const stompClient = Stomp.over(new SockJS('/ws'));

// Subscribe to turn updates
stompClient.subscribe(`/topic/turnsInitialized/${lobbyId}`, (response) => {
  const turnState = JSON.parse(response.body);
  updateGameUI(turnState);
});

// Subscribe to dice roll results
stompClient.subscribe(`/topic/diceRolled/${lobbyId}`, (response) => {
  const turnState = JSON.parse(response.body);
  handleDiceRollResult(turnState);
});

// Subscribe to movement completion
stompClient.subscribe(`/topic/movementCompleted/${lobbyId}`, (response) => {
  const turnState = JSON.parse(response.body);
  handleMovementComplete(turnState);
});

// Subscribe to suggestion results
stompClient.subscribe(`/topic/suggestionMade/${lobbyId}`, (response) => {
  const result = JSON.parse(response.body);
  handleSuggestionResult(result);
});

// Subscribe to accusation results
stompClient.subscribe(`/topic/accusationMade/${lobbyId}`, (response) => {
  const result = JSON.parse(response.body);
  handleAccusationResult(result);
});

// Roll dice
function rollDice(playerName) {
  stompClient.send(`/app/rollDice/${lobbyId}`, {}, JSON.stringify({
    playerName: playerName,
    actionType: "DICE_ROLL"
  }));
}

// Complete movement
function completeMovement(playerName) {
  stompClient.send(`/app/completeMovement/${lobbyId}`, {}, JSON.stringify({
    playerName: playerName,
    actionType: "MOVEMENT"
  }));
}

// Make suggestion
function makeSuggestion(playerName, suspect, weapon, room) {
  stompClient.send(`/app/makeSuggestion/${lobbyId}`, {}, JSON.stringify({
    playerName: playerName,
    suspect: suspect,
    weapon: weapon,
    room: room
  }));
}

// Make accusation
function makeAccusation(playerName, suspect, weapon, room) {
  stompClient.send(`/app/makeAccusation/${lobbyId}`, {}, JSON.stringify({
    playerName: playerName,
    suspect: suspect,
    weapon: weapon,
    room: room
  }));
}

// Get current turn state
function getTurnState() {
  stompClient.send(`/app/getTurnState/${lobbyId}`, {}, "");
}

// Check if it's a player's turn
function checkPlayerTurn(playerName) {
  stompClient.send(`/app/checkPlayerTurn/${lobbyId}`, {}, JSON.stringify({
    playerName: playerName
  }));
}
```

## Game Flow

1. **Game Start**: Send `/app/initializeTurns/{lobbyId}` to begin turn-based gameplay
2. **Player Turn**: Current player receives `PLAYERS_TURN_ROLL_DICE` state
3. **Roll Dice**: Player sends `/app/rollDice/{lobbyId}` and receives dice value
4. **Movement**: Player moves on board, then sends `/app/completeMovement/{lobbyId}`
5. **Optional Actions**: 
   - If in room: Can make suggestion via `/app/makeSuggestion/{lobbyId}`
   - Anytime during turn: Can make accusation via `/app/makeAccusation/{lobbyId}`
6. **Turn End**: System automatically moves to next player

## Error Handling

All endpoints return error responses in case of invalid actions:
```javascript
{
  success: false,
  message: "Error description",
  lobbyId: "lobby123"
}
```

## Notes

- Replace `{lobbyId}` with the actual lobby ID in all endpoints
- All requests should include proper player authentication
- The turn system enforces game rules and prevents invalid actions
- Subscribe to all relevant topics before sending any requests
- Handle connection errors and reconnection logic in your frontend

