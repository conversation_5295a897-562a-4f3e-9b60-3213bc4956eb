package at.aau.se2.cluedo.data.models

import com.google.gson.annotations.SerializedName

/**
 * Data classes for turn-based gameplay functionality
 */

data class TurnActionRequest(
    @SerializedName("playerName") val playerName: String,
    @SerializedName("actionType") val actionType: String, // "DICE_ROLL", "MOVEMENT", "SUGGESTION", "ACCUSATION"
    @SerializedName("diceValue") val diceValue: Int = 0,
    @SerializedName("suspect") val suspect: String = "",
    @SerializedName("weapon") val weapon: String = "",
    @SerializedName("room") val room: String = ""
)

data class TurnStateResponse(
    @SerializedName("lobbyId") val lobbyId: String,
    @SerializedName("currentPlayerName") val currentPlayerName: String,
    @SerializedName("currentPlayerIndex") val currentPlayerIndex: Int,
    @SerializedName("turnState") val turnState: String, // See TurnState enum
    @SerializedName("canMakeAccusation") val canMakeAccusation: Boolean,
    @SerializedName("canMakeSuggestion") val canMakeSuggestion: Boolean,
    @SerializedName("diceValue") val diceValue: Int,
    @SerializedName("message") val message: String
)

data class SuggestionRequest(
    @SerializedName("playerName") val playerName: String,
    @SerializedName("suspect") val suspect: String,
    @SerializedName("weapon") val weapon: String,
    @SerializedName("room") val room: String
)

data class AccusationRequest(
    @SerializedName("playerName") val playerName: String,
    @SerializedName("suspect") val suspect: String,
    @SerializedName("weapon") val weapon: String,
    @SerializedName("room") val room: String
)

data class PlayerTurnCheckRequest(
    @SerializedName("playerName") val playerName: String
)

/**
 * Turn states as defined in the backend API
 */
enum class TurnState(val value: String) {
    WAITING_FOR_PLAYERS("WAITING_FOR_PLAYERS"),
    WAITING_FOR_START("WAITING_FOR_START"),
    PLAYERS_TURN_ROLL_DICE("PLAYERS_TURN_ROLL_DICE"),
    PLAYERS_TURN_MOVE("PLAYERS_TURN_MOVE"),
    PLAYERS_TURN_SUSPECT("PLAYERS_TURN_SUSPECT"),
    PLAYERS_TURN_SOLVE("PLAYERS_TURN_SOLVE"),
    PLAYERS_TURN_END("PLAYERS_TURN_END"),
    PLAYER_HAS_WON("PLAYER_HAS_WON");

    companion object {
        fun fromString(value: String): TurnState {
            return values().find { it.value == value } ?: WAITING_FOR_PLAYERS
        }
    }
}

/**
 * Action types for turn actions
 */
enum class ActionType(val value: String) {
    DICE_ROLL("DICE_ROLL"),
    MOVEMENT("MOVEMENT"),
    SUGGESTION("SUGGESTION"),
    ACCUSATION("ACCUSATION")
}
